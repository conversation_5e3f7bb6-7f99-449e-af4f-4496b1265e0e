package org.example.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AiService {

    private final Logger logger = LoggerFactory.getLogger(AiService.class);

    @Value("${bailian.api.key:fake-api-key-for-bailian}")
    private String apiKey;
    
    @Value("${bailian.api.url:https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions}")
    private String apiUrl;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public AiService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }
    
    public String parseLogisticsRules(String pdfText) throws JsonProcessingException {
        // 构建提示词
        String prompt = buildPrompt(pdfText);
        
        // 构建请求
        Map<String, Object> request = new HashMap<>();
        request.put("model", "qwen-max"); // 使用百炼支持的模型 qwen-max
        request.put("temperature", 0.01); // 降低温度以获得更确定性的输出
        request.put("max_tokens", 8000); // 增加输出长度限制
        
        List<Map<String, String>> messages = new ArrayList<>();
        messages.add(Map.of("role", "system", "content", 
            "你是一位物流定价专家。请从物流定价文档中提取结构化数据，并按照指定格式输出JSON。你必须严格按照示例格式输出，不要添加任何额外的解释或文本。"));
        messages.add(Map.of("role", "user", "content", prompt));
        request.put("messages", messages);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
        
        try {
            logger.info("发送请求到百炼API: {}", apiUrl);
            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);
            logger.info("收到百炼API响应: {}", response.getStatusCode());
            
            // 解析响应
            JsonNode rootNode = objectMapper.readTree(response.getBody());
            String content = rootNode.path("choices").get(0).path("message").path("content").asText();
            
            // 确保返回的是有效JSON
            try {
                objectMapper.readTree(content);
                logger.info("成功解析大模型返回的JSON");
                return content;
            } catch (Exception e) {
                logger.warn("大模型返回的不是有效JSON，尝试提取JSON部分", e);
                // 如果返回的不是JSON，尝试提取JSON部分
                return extractJsonFromText(content);
            }
        } catch (Exception e) {
            logger.error("调用百炼API失败，使用模拟数据: {}", e.getMessage(), e);
            // 模拟响应，用于测试
            return generateMockResponse();
        }
    }
    
    private String buildPrompt(String pdfText) {
        return   "  //重要规则：\n" +
                "  一个 quote 有多个 route；\n" +
                "  一个 routes 只有一个起运港、目的港， 对应一组rates；\n" +
                "  *** 非常重要，文本中的基础价格可能是二维的，请务必拉平，全部提取出来。一组rates中， 包含一个或多个密度density_factor； 一个密度density_factor会有多个价格等级如 100KG 300KG 每一个价格等级有自己的price  ，必须仔细检查上下文； 每一个rate包含一个密度density_factor、一个价格等级有自己的price 如{ \"min_wt\": 45, \"max_wt\": 100, \"price\": 5.1, \"density_type\": \"Y\", \"density_factor\": 200 },\n" +
                "{ \"min_wt\": 100, \"max_wt\": 200, \"price\": 4.7, \"density_type\": \"Y\", \"density_factor\": 200 }"+
                "以下是从物流定价PDF文档中提取的文本。请分析并以JSON格式提取以下信息，格式必须严格按照示例：\n\n" +
                "{"+
               "  \"currency\": \"CNY\",\n" +
               "  \"quote_date\": \"2023-10-17\",\n" +
               "  \"quote_number\": \"\",\n" +
               "  \"remarks\": [\n" +
               "    {\n" +
               "      \"content\": \"以上价格已包括空运附加费\",\n" +
               "      \"is_important\": true,\n" +
               "      \"type\": \"GENERAL\"\n" +
               "    }\n" +
               "  ],\n" +
               "  \"routes\": [\n" +
               "    {\n" +
               "      \"M-rate\": 0,\n" +
               "      \"N-rate\": 0,\n" +
               "      \"carrier_code\": \"LH\",\n" +
               "      \"destination\": \"FRA\",\n" +
               "      \"frequency\": \"DAILY\",\n" +
               "      \"origin\": \"PEK\",\n" +
               "      \"rates\": [\n" +
               "        {\n" +
               "          \"density_factor\": 80,\n" +
               "          \"density_type\": \"Y\",\n" +
               "          \"max_wt\": 300,\n" +
               "          \"min_wt\": 100,\n" +
               "          \"price\": 26\n" +
               "        }\n" +
               "      ],\n" +
               "      \"surcharges\": [  {\n" +
                "            \"surcharges_name\":\"\",\n" +
                "            \"surcharges_cost\":\"\",\n" +
                "        }],\n" +
               "      \"transfers\": [        {\n" +
                "          \"cost_currency\": \"CNY\",\n" +
                "          \"destinations\": [\n" +
                "            {\n" +
                "              \"to_port\": \"AMS\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"to_port\": \"BRU\"\n" +
                "            }\n" +
                "          ],\n" +
                "          \"from_port\": \"PEK\",\n" +
                "          \"transfer_cost\": 1\n" +
                "        }\n" +
                "]\n" +
                "    }]\n" +
               "    }\n" +
               "  ],\n" +
               "  \"source_file\": \"\",\n" +
               "  \"status\": \"ACTIVE\",\n" +
               "  \"valid_from\": \"2024-05-29\",\n" +
               "  \"valid_to\": \"2024-08-27\",\n" +
               "  \"vendor_code\": \"BJZK\"\n" +
               "}\n\n" +
               "请确保输出的JSON格式与示例完全一致，包括字段名称和数据类型。如果文字描述中包含其他报价信息，放在附加费surcharges里,这些附加费就不用放在备注里了，" +
                "如果某些信息在文档中不存在，请使用合理的默认值或空值。\n\n" +
               "请只输出JSON，不要添加任何额外的解释或文本。\n\n" +
               "文档文本：\n" + pdfText;
    }
    
    private String extractJsonFromText(String text) {
        // 尝试从文本中提取JSON部分
        int startIndex = text.indexOf("{");
        int endIndex = text.lastIndexOf("}");
        
        if (startIndex >= 0 && endIndex > startIndex) {
            return text.substring(startIndex, endIndex + 1);
        }
        
        // 如果无法提取，返回简单的错误JSON
        return "{\"error\": \"无法从AI响应中提取有效的JSON\"}";
    }
    
    // 仅用于测试，实际应用中应该删除此方法
    private String generateMockResponse() {
        // 生成模拟数据用于测试，使用与json.txt相同的格式
        return "{\n" +
               "  \"currency\": \"CNY\",\n" +
               "  \"quote_date\": \"2023-10-17\",\n" +
               "  \"quote_number\": \"LH20231017\",\n" +
               "  \"remarks\": [\n" +
               "    {\n" +
               "      \"content\": \"以上价格已包括空运附加费\",\n" +
               "      \"is_important\": true,\n" +
               "      \"type\": \"GENERAL\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"content\": \"以上价格只适用于普货\",\n" +
               "      \"is_important\": true,\n" +
               "      \"type\": \"GENERAL\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"content\": \"汉莎包板4月22日起变为DAILY\",\n" +
               "      \"is_important\": false,\n" +
               "      \"type\": \"GENERAL\"\n" +
               "    }\n" +
               "  ],\n" +
               "  \"routes\": [\n" +
               "    {\n" +
               "      \"M-rate\": 0,\n" +
               "      \"N-rate\": 0,\n" +
               "      \"carrier_code\": \"LH\",\n" +
               "      \"destination\": \"FRA\",\n" +
               "      \"frequency\": \"DAILY\",\n" +
               "      \"origin\": \"PEK\",\n" +
               "      \"rates\": [\n" +
               "        {\n" +
               "          \"density_factor\": 80,\n" +
               "          \"density_type\": \"Y\",\n" +
               "          \"max_wt\": 300,\n" +
               "          \"min_wt\": 100,\n" +
               "          \"price\": 26\n" +
               "        }\n" +
               "      ],\n" +
               "      \"surcharges\": [],\n" +
               "      \"transfers\": [\n" +
               "        {\n" +
               "          \"cost_currency\": \"CNY\",\n" +
               "          \"destinations\": [\n" +
               "            {\n" +
               "              \"to_port\": \"AMS\"\n" +
               "            },\n" +
               "            {\n" +
               "              \"to_port\": \"BRU\"\n" +
               "            }\n" +
               "          ],\n" +
               "          \"from_port\": \"FRA\",\n" +
               "          \"transfer_cost\": 1\n" +
               "        }\n" +
               "      ]\n" +
               "    }\n" +
               "  ],\n" +
               "  \"source_file\": \"\",\n" +
               "  \"status\": \"ACTIVE\",\n" +
               "  \"valid_from\": \"2024-05-29\",\n" +
               "  \"valid_to\": \"2024-08-27\",\n" +
               "  \"vendor_code\": \"BJZK\"\n" +
               "}";
    }
}